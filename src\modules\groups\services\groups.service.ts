import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
  ForbiddenException,
  Inject,
  forwardRef,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { Group } from '../entities/group.entity';
import { GroupMember } from '../entities/group-member.entity';
import { CreateGroupDto } from '../dto/create-group.dto';
import { OrgMember } from '../../members/entities/org-member.entity';
import { User } from '../../users/entities/user.entity';
import { Organization } from '../../organization/entities/organization.entity';
import { AllocateMemberDto } from '../dto/allocate-member.dto';
import { MailService } from '../../../core/mail/mail.service';
import { getISTTimeString } from '../../../common/utils/date.utils';
import { GroupEncryptionService } from '../../security/services/group-encryption.service';
import { ConfigService } from '@nestjs/config';
import { plainToInstance } from 'class-transformer';
import { UpdateGroupDto } from '../dto/update-group.dto';
import { UpdateGroupMemberDto } from '../dto/update.groupmember.dto';
import { SocketGateway } from '../../../infrastructure/socket/socket.gateway';
import { StorageService } from '../../../core/storage/storage.service';

@Injectable()
export class GroupsService {
  constructor(
    @InjectRepository(Group)
    private groupRepository: Repository<Group>,
    @InjectRepository(GroupMember)
    private groupMemberRepository: Repository<GroupMember>,
    @InjectRepository(OrgMember)
    private orgMemberRepository: Repository<OrgMember>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(GroupMember)
    private readonly groupMemberRepo: Repository<GroupMember>,
    @InjectRepository(Organization)
    private organizationRepository: Repository<Organization>,
    private dataSource: DataSource,
    private mailService: MailService,
    private readonly configService: ConfigService,
    private readonly groupEncryptionService: GroupEncryptionService,
    @Inject(forwardRef(() => SocketGateway))
    private readonly socketGateway: SocketGateway,
    private readonly storageService: StorageService,
  ) {}

  /**
   * Create a new group with the creator as the first member
   */
  async create(createGroupDto: CreateGroupDto, userId): Promise<Group> {
    // Verify creator exists
    const creator = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!creator) {
      throw new NotFoundException(`Admin with ID ${userId} not found`);
    }
    if (creator.roleId === 1) {
      throw new BadRequestException(
        'Product admin is not allowed to create organization members',
      );
    }

    const existingGroupname = await this.groupRepository.findOne({
      where: { name: createGroupDto.name, orgId: creator.orgId },
    });

    if (existingGroupname) {
      throw new ConflictException('Group with this name already exists');
    }

    // Verify that the organization exists
    const organization = await this.organizationRepository.findOne({
      where: { id: creator.orgId },
    });

    if (!organization) {
      throw new NotFoundException(
        `Organization with ID ${creator.orgId} not found`,
      );
    }

    // Create group with transaction to ensure data consistency
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Create the group
      const group = this.groupRepository.create({
        name: createGroupDto.name,
        description: createGroupDto.description,
        orgId: creator.orgId,
        imageUrl: createGroupDto.fileUrl,
        createdBy: userId,
        currentKeyVersion: 1,
      });

      const savedGroup = await queryRunner.manager.save(group);

      await queryRunner.commitTransaction();

      return this.findOne(savedGroup.id);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async updateGroup(
    id: number,
    updateGroupDto: UpdateGroupDto,
    userId: number,
  ): Promise<Group> {
    const group = await this.groupRepository.findOne({
      where: { id },
      withDeleted: true,
    });

    if (!group) {
      throw new NotFoundException(`Group with ID ${id} not found`);
    }

    // Update basic fields
    await this.groupRepository.update(id, {
      name: updateGroupDto.name,
      imageUrl: updateGroupDto.fileUrl,
      description: updateGroupDto.description,
    });

    // Handle isActive toggle
    if (!updateGroupDto.isActive) {
      await this.groupRepository.update(id, {
        deletedAt: new Date(),
        deletedBy: userId,
      });
    } else if (updateGroupDto.isActive) {
      await this.groupRepository.update(id, {
        deletedAt: null as any,
        deletedBy: null as any,
      });
    }

    const updatedGroup = await this.groupRepository.findOne({
      where: { id },
      withDeleted: true,
    });

    if (!updatedGroup) {
      throw new NotFoundException(`Group with ID ${id} not found after update`);
    }

    return updatedGroup; // Return Group type only
  }

  /**
   * Find a group by ID with related entities
   */
  async findOne(id: number): Promise<any> {
    const group = await this.groupRepository.findOne({
      where: { id },
      relations: ['organization', 'creator'],
    });

    if (!group) {
      throw new NotFoundException(`Group with ID ${id} not found`);
    }

    const { password, username, ...creatorWithoutSensitive } =
      group.creator ?? {};

    return {
      ...group,
      creator: creatorWithoutSensitive,
    };
  }

  async deleteGroup(id: number, userId: number): Promise<void> {
    const group = await this.groupRepository.findOne({
      where: { id },
      withDeleted: true,
    });

    if (!group) {
      throw new NotFoundException(`Group with ID ${id} not found`);
    }

    if (group.deletedAt) {
      throw new BadRequestException(`Group with ID ${id} is already deleted`);
    }

    if (group.createdBy !== userId) {
      throw new ForbiddenException(
        `You are not authorized to delete this group`,
      );
    }

    await this.groupRepository.update(id, {
      deletedAt: new Date(),
      deletedBy: userId,
    });
    const activeMembers = await this.groupMemberRepository.find({
      where: { groupId: id, isActive: true },
    });

    const memberIds = activeMembers.map((m) => m.memberId);

    // 📣 Broadcast deletion to each member
    for (const memberId of memberIds) {
      this.socketGateway.broadcastToMember(memberId, 'group_deleted', {
        groupId: id,
      });
    }
  }

  async findGroupWithMembers(groupId: number): Promise<Group> {
    const group = await this.groupRepository.findOne({
      where: { id: groupId },
      relations: ['members', 'members.member'],
    });

    if (!group) {
      throw new NotFoundException(`Group with id ${groupId} not found`);
    }

    return group;
  }

  async allocateMembersToGroup(
    dto: AllocateMemberDto,
    userId,
  ): Promise<GroupMember[]> {
    const appName = this.configService.get<string>('APP_NAME', 'ChatApp');
    const { groupId, memberIds, adminSecretKey } = dto;

    if (!adminSecretKey) {
      throw new Error('Admin secret key is required for encryption operations');
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Check group exists
      const group = await queryRunner.manager.findOne(
        this.groupRepository.target,
        {
          where: { id: groupId },
          relations: ['organization', 'creator'],
        },
      );

      if (!group) {
        throw new NotFoundException(`Group with ID ${groupId} not found`);
      }

      if (Number(group.createdBy) !== Number(userId)) {
        throw new ForbiddenException(
          `You are not authorized to allocate members to this group`,
        );
      }

      // Get existing group members to use for key rotation
      const existingGroupMembers = await queryRunner.manager.find(
        this.groupMemberRepository.target,
        {
          where: { groupId, isActive: true },
        },
      );

      const existingMemberIds = existingGroupMembers.map(
        (member) => member.memberId,
      );
      const allocatedMembers: GroupMember[] = [];
      const newMemberIds: number[] = [];

      // Validate members and create group memberships
      for (const memberId of memberIds) {
        // Check member exists
        const member = await queryRunner.manager.findOne(
          this.orgMemberRepository.target,
          {
            where: { id: memberId },
          },
        );

        if (!member) {
          throw new NotFoundException(`Member with ID ${memberId} not found`);
        }

        // Check if already a member
        const existing = await queryRunner.manager.findOne(
          this.groupMemberRepository.target,
          {
            where: { groupId, memberId, isActive: true },
          },
        );

        if (existing) {
          throw new ConflictException(
            `Member with ID ${memberId} is already in the group`,
          );
        }

        // Create the group membership
        const newMembership = this.groupMemberRepository.create({
          groupId,
          memberId,
          joinedAt: getISTTimeString(),
        });

        const savedMembership = await queryRunner.manager.save(newMembership);
        allocatedMembers.push(savedMembership);
        newMemberIds.push(memberId);

        // Send notification to the user
        if (member?.email) {
          await this.mailService.sendMemberNotification(
            member.email,
            member.name || 'Member',
            group.organization.name,
            group.name,
            appName,
            member.phoneNo,
          );
        }
      }

      // Handle encryption key rotation for all members (existing + new)
      await this.groupEncryptionService.allocateMembersWithKeyRotation(
        groupId,
        adminSecretKey,
        newMemberIds,
        existingMemberIds,
      );

      await queryRunner.commitTransaction();

      await this.emitGroupInfoToNewMembers(group, newMemberIds, [
        ...existingGroupMembers,
        ...allocatedMembers,
      ]);

      return allocatedMembers;
    } catch (error) {
      console.error('Allocation failed:', error);
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async findAllByOrgId(orgId: number): Promise<any[]> {
    const groups = await this.groupRepository.find({
      where: { orgId },
      relations: ['members'],
      withDeleted: true,
    });

    const groupData = groups.map((group) => ({
      ...group,
      isActive: group.deletedAt == null,
    }));

    return plainToInstance(Group, groupData, {
      excludeExtraneousValues: false,
    });
  }

  async updateGroupMembers(
    dto: UpdateGroupMemberDto,
    userId,
  ): Promise<GroupMember[]> {
    const { groupId, updatedMemberIds, adminSecretKey } = dto;

    if (!adminSecretKey) {
      throw new Error('Admin secret key is required for encryption operations');
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Check group exists
      const group = await queryRunner.manager.findOne(
        this.groupRepository.target,
        {
          where: { id: groupId },
          relations: ['organization', 'creator'],
        },
      );

      if (!group) {
        throw new NotFoundException(`Group with ID ${groupId} not found`);
      }

      if (Number(group.createdBy) !== Number(userId)) {
        throw new ForbiddenException(
          `You are not authorized to allocate members to this group`,
        );
      }

      // Get existing group members to use for key rotation
      const existingGroupMembers = await queryRunner.manager.find(
        this.groupMemberRepository.target,
        {
          where: { groupId, isActive: true },
        },
      );

      const existingMemberIds = existingGroupMembers.map(
        (member) => member.memberId,
      );

      // Determine members to remove and to add
      const membersToRemove = existingMemberIds.filter(
        (id) => !updatedMemberIds.includes(id),
      );
      const membersToAdd = updatedMemberIds.filter(
        (id) => !existingMemberIds.includes(id),
      );

      // Remove members
      for (const memberId of membersToRemove) {
        await queryRunner.manager.update(
          this.groupMemberRepository.target,
          { groupId, memberId, isActive: true },
          { isActive: false, leftAt: getISTTimeString() },
        );
        const member = await queryRunner.manager.findOne(
          this.orgMemberRepository.target,
          { where: { id: memberId } },
        );

        if (member) {
          this.socketGateway.emitGroupEvent(groupId, 'group_member_left', {
            groupId,
            memberId,
            memberName: member.name,
            message: `${member.name} left`,
            status: 'left',
          });
        }
      }

      // Add new members
      const addedMembers: GroupMember[] = [];
      for (const memberId of membersToAdd) {
        // Check member exists
        const member = await queryRunner.manager.findOne(
          this.orgMemberRepository.target,
          {
            where: { id: memberId },
          },
        );

        if (!member) {
          throw new NotFoundException(`Member with ID ${memberId} not found`);
        }

        // Create the group membership
        const newMembership = this.groupMemberRepository.create({
          groupId,
          memberId,
          joinedAt: getISTTimeString(),
        });

        const savedMembership = await queryRunner.manager.save(newMembership);
        addedMembers.push(savedMembership);
        // Emit join event
        this.socketGateway.emitGroupEvent(groupId, 'group_member_joined', {
          groupId,
          memberId,
          memberName: member.name,
          message: `${member.name} joined`,
          status: 'join',
        });
      }

      // Handle encryption key rotation for all members (updated list)
      await this.groupEncryptionService.allocateMembersWithKeyRotation(
        groupId,
        adminSecretKey,
        membersToAdd,
        existingMemberIds,
      );

      await queryRunner.commitTransaction();

      // Return the updated list of active group members
      const updatedGroupMembers = await this.groupMemberRepository.find({
        where: { groupId, isActive: true },
      });
      await this.emitGroupInfoToNewMembers(
        group,
        membersToAdd,
        updatedGroupMembers,
      );
      return updatedGroupMembers;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async getAllGroupsWithMembers(orgId: number) {
    const groups = await this.groupRepository.find({
      where: { orgId, isActive: true },
      relations: ['members', 'members.member'],
    });

    return groups
      .filter((group) => group.members?.some((gm) => gm.isActive && gm.member))
      .map((group) => ({
        id: group.id,
        groupId: group.id,
        groupName: group.name,
        createdAt: group.createdAt.toISOString().split('T')[0],
        members: group.members
          .filter((gm) => gm.isActive && gm.member)
          .map((gm) => ({
            memberId: gm.member?.id,
            name: gm.member?.name || 'Unknown',
            imageUrl: gm.member?.imageUrl,
            joiningDate: gm.joinedAt.toISOString().split('T')[0],
          })),
      }));
  }

  async emitGroupInfoToNewMembers(
    group: Group,
    memberIds: number[],
    members: GroupMember[],
  ) {
    for (const memberId of memberIds) {
      // You need to fetch groupKey and memberPublicKey before calling encryptGroupKeyForMember
      // Example (replace with actual logic to get these values):
      const groupKey = ''; // TODO: Retrieve the actual group key for the group
      const member = members.find((m) => m.memberId === memberId);
      const memberPublicKey = member?.publicKey || ''; // TODO: Retrieve the actual public key for the member

      const encryptedGroupKey = await this.groupEncryptionService.encryptGroupKeyForMember(
        groupKey,
        memberPublicKey,
      );

      const admin = group.creator?.username;

      const payload = {
        id: group.id,
        type: 'group',
        name: group.name,
        encryptedGroupKey: encryptedGroupKey || '',
        date: group.createdAt.toISOString(),
        imageUrl: signedUrl,
        msg: '', // empty because it's a fresh member
        msgNonce: '',
        msgGroupKeyVersion: '',
        read: true,
        unreadCount: 0,
        members, // full member list
        lastSender: null,
        lastSenderID: null,
        createdAt: group.createdAt.toISOString(),
        admin: admin,
      };
        lastSender: null,
        lastSenderID: null,
        createdAt: group.createdAt.toISOString(),
        admin: admin,
      };

      this.socketGateway.broadcastToMember(
        memberId,
        'new_group_allocated',
        payload,
      );
    }
  }
}
